import PyPDF2
import pdfplumber
import re
from typing import List, Dict, Optional
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFExtractor:
    """PDF文本提取器，支持多种提取方法"""
    
    def __init__(self):
        self.extracted_text = ""
        self.page_texts = []
    
    def extract_with_pypdf2(self, pdf_path: str) -> str:
        """使用PyPDF2提取PDF文本"""
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                self.page_texts = []
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    self.page_texts.append({
                        'page_num': page_num + 1,
                        'text': page_text
                    })
                    text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
                
                self.extracted_text = text
                return text
        except Exception as e:
            logger.error(f"PyPDF2提取失败: {e}")
            return ""
    
    def extract_with_pdfplumber(self, pdf_path: str) -> str:
        """使用pdfplumber提取PDF文本（通常效果更好）"""
        try:
            text = ""
            self.page_texts = []
            
            with pdfplumber.open(pdf_path) as pdf:
                for page_num, page in enumerate(pdf.pages):
                    page_text = page.extract_text()
                    if page_text:
                        self.page_texts.append({
                            'page_num': page_num + 1,
                            'text': page_text
                        })
                        text += f"\n--- Page {page_num + 1} ---\n{page_text}\n"
                
                self.extracted_text = text
                return text
        except Exception as e:
            logger.error(f"pdfplumber提取失败: {e}")
            return ""
    
    def extract_text(self, pdf_path: str, method: str = "pdfplumber") -> str:
        """提取PDF文本的主方法"""
        logger.info(f"开始提取PDF文本: {pdf_path}")
        
        if method == "pdfplumber":
            text = self.extract_with_pdfplumber(pdf_path)
            if not text:  # 如果pdfplumber失败，尝试PyPDF2
                logger.info("pdfplumber失败，尝试PyPDF2")
                text = self.extract_with_pypdf2(pdf_path)
        else:
            text = self.extract_with_pypdf2(pdf_path)
            if not text:  # 如果PyPDF2失败，尝试pdfplumber
                logger.info("PyPDF2失败，尝试pdfplumber")
                text = self.extract_with_pdfplumber(pdf_path)
        
        if text:
            # 清理文本
            text = self.clean_text(text)
            logger.info(f"成功提取文本，长度: {len(text)} 字符")
        else:
            logger.error(f"无法提取PDF文本: {pdf_path}")
        
        return text
    
    def clean_text(self, text: str) -> str:
        """清理提取的文本"""
        # 移除多余的空白字符
        text = re.sub(r'\n\s*\n', '\n\n', text)
        text = re.sub(r' +', ' ', text)
        
        # 移除页眉页脚等重复内容（简单处理）
        lines = text.split('\n')
        cleaned_lines = []
        
        for line in lines:
            line = line.strip()
            if line and len(line) > 3:  # 过滤掉太短的行
                cleaned_lines.append(line)
        
        return '\n'.join(cleaned_lines)
    
    def chunk_text(self, text: str, chunk_size: int = 4000, overlap: int = 200) -> List[str]:
        """将文本分块处理"""
        if len(text) <= chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # 尝试在句号处分割
            if end < len(text):
                # 寻找最近的句号
                last_period = text.rfind('.', start, end)
                if last_period > start + chunk_size // 2:
                    end = last_period + 1
            
            chunk = text[start:end]
            chunks.append(chunk)
            
            if end >= len(text):
                break
                
            start = end - overlap
        
        return chunks
    
    def get_page_info(self) -> List[Dict]:
        """获取页面信息"""
        return self.page_texts
