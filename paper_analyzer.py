import os
import json
import time
from datetime import datetime
from typing import Dict, List, Set
from tqdm import tqdm
import logging

from pdf_extractor import PDFExtractor
from ai_analyzer import AIAnalyzer
from config import *

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PaperAnalyzer:
    """论文分析器主类"""
    
    def __init__(self, model_type: str = DEFAULT_MODEL):
        self.pdf_extractor = PDFExtractor()
        self.ai_analyzer = AIAnalyzer(model_type)
        self.results = {}
        self.processed_files = set()
        
        # 加载现有结果和进度
        self.load_existing_results()
        self.load_progress()
    
    def load_existing_results(self):
        """加载现有的分析结果"""
        if os.path.exists(OUTPUT_JSON):
            try:
                with open(OUTPUT_JSON, 'r', encoding='utf-8') as f:
                    self.results = json.load(f)
                logger.info(f"加载了 {len(self.results)} 个现有分析结果")
            except Exception as e:
                logger.error(f"加载现有结果失败: {e}")
                self.results = {}
    
    def load_progress(self):
        """加载分析进度"""
        if os.path.exists(PROGRESS_FILE):
            try:
                with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                    progress_data = json.load(f)
                    self.processed_files = set(progress_data.get('processed_files', []))
                logger.info(f"加载了 {len(self.processed_files)} 个已处理文件的记录")
            except Exception as e:
                logger.error(f"加载进度失败: {e}")
                self.processed_files = set()
    
    def save_progress(self):
        """保存分析进度"""
        progress_data = {
            'processed_files': list(self.processed_files),
            'last_updated': datetime.now().isoformat(),
            'total_processed': len(self.processed_files)
        }
        
        try:
            with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"保存进度失败: {e}")
    
    def save_results(self):
        """保存分析结果"""
        try:
            with open(OUTPUT_JSON, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)
            logger.info(f"保存了 {len(self.results)} 个分析结果到 {OUTPUT_JSON}")
        except Exception as e:
            logger.error(f"保存结果失败: {e}")
    
    def get_pdf_files(self) -> List[str]:
        """获取所有PDF文件"""
        pdf_files = []
        if os.path.exists(PDF_FOLDER):
            for filename in os.listdir(PDF_FOLDER):
                if filename.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(PDF_FOLDER, filename))
        return pdf_files
    
    def analyze_single_paper(self, pdf_path: str) -> Dict:
        """分析单个论文"""
        logger.info(f"开始分析论文: {pdf_path}")
        
        # 提取PDF文本
        text = self.pdf_extractor.extract_text(pdf_path)
        if not text:
            logger.error(f"无法提取PDF文本: {pdf_path}")
            return {"error": "无法提取PDF文本"}
        print(len(text))
        exit(0)
        # 将文本分块
        chunks = self.pdf_extractor.chunk_text(text, CHUNK_SIZE, OVERLAP_SIZE)
        logger.info(f"文本分为 {len(chunks)} 个块进行分析")
        
        # 分析每个文本块
        chunk_results = []
        for i, chunk in enumerate(tqdm(chunks, desc="分析文本块")):
            logger.info(f"分析第 {i+1}/{len(chunks)} 个文本块")
            result = self.ai_analyzer.analyze_text_chunk(chunk)
            if result:
                chunk_results.append(result)
            time.sleep(1)  # 避免API限制
        
        if not chunk_results:
            logger.error(f"所有文本块分析都失败: {pdf_path}")
            return {"error": "AI分析失败"}
        
        # 合并分析结果
        merged_result = self.ai_analyzer.merge_analysis_results(chunk_results)
        
        # 添加元数据
        merged_result.update({
            "file_path": pdf_path,
            "file_name": os.path.basename(pdf_path),
            "analysis_date": datetime.now().isoformat(),
            "text_length": len(text),
            "chunks_analyzed": len(chunk_results),
            "total_chunks": len(chunks)
        })
        
        return merged_result
    
    def analyze_all_papers(self, force_reanalyze: bool = False):
        """分析所有论文"""
        pdf_files = self.get_pdf_files()
        
        if not pdf_files:
            logger.warning(f"在 {PDF_FOLDER} 中没有找到PDF文件")
            return
        
        logger.info(f"找到 {len(pdf_files)} 个PDF文件")
        
        # 过滤已处理的文件
        if not force_reanalyze:
            files_to_process = [f for f in pdf_files if f not in self.processed_files]
            logger.info(f"需要处理 {len(files_to_process)} 个新文件")
        else:
            files_to_process = pdf_files
            logger.info("强制重新分析所有文件")
        
        if not files_to_process:
            logger.info("没有需要处理的新文件")
            return
        
        # 逐个分析文件
        for pdf_path in tqdm(files_to_process, desc="分析论文"):
            try:
                logger.info(f"\n{'='*50}")
                logger.info(f"正在处理: {os.path.basename(pdf_path)}")
                logger.info(f"{'='*50}")
                
                # 分析论文
                result = self.analyze_single_paper(pdf_path)
                
                # 保存结果
                file_key = os.path.basename(pdf_path)
                self.results[file_key] = result
                
                # 标记为已处理
                self.processed_files.add(pdf_path)
                
                # 立即保存（增量保存）
                self.save_results()
                self.save_progress()
                
                logger.info(f"完成分析: {file_key}")
                
            except Exception as e:
                logger.error(f"分析文件失败 {pdf_path}: {e}")
                # 即使失败也要保存进度
                self.save_progress()
                continue
        
        logger.info(f"\n分析完成！总共处理了 {len(self.results)} 个文件")
    
    def get_analysis_summary(self) -> Dict:
        """获取分析摘要"""
        if not self.results:
            return {"message": "没有分析结果"}
        
        summary = {
            "total_papers": len(self.results),
            "successful_analyses": 0,
            "failed_analyses": 0,
            "total_hypotheses": 0,
            "total_questions": 0,
            "papers_with_hypotheses": 0,
            "papers_with_questions": 0
        }
        
        for paper_name, result in self.results.items():
            if "error" in result:
                summary["failed_analyses"] += 1
            else:
                summary["successful_analyses"] += 1
                
                hypotheses = result.get("research_hypotheses", [])
                questions = result.get("research_questions", [])
                
                summary["total_hypotheses"] += len(hypotheses)
                summary["total_questions"] += len(questions)
                
                if hypotheses:
                    summary["papers_with_hypotheses"] += 1
                if questions:
                    summary["papers_with_questions"] += 1
        
        return summary

def main():
    """主函数"""
    print("论文研究假设分析器")
    print("=" * 50)
    
    # 检查API密钥
    if DEFAULT_MODEL == "openai" and not OPENAI_API_KEY:
        print("错误: 请设置OPENAI_API_KEY环境变量")
        return
    elif DEFAULT_MODEL == "deepseek" and not DEEPSEEK_API_KEY:
        print("错误: 请设置DEEPSEEK_API_KEY环境变量")
        return
    
    # 创建分析器
    analyzer = PaperAnalyzer(DEFAULT_MODEL)
    
    # 显示当前状态
    print(f"使用模型: {DEFAULT_MODEL}")
    print(f"PDF文件夹: {PDF_FOLDER}")
    print(f"输出文件: {OUTPUT_JSON}")
    print(f"已处理文件: {len(analyzer.processed_files)}")
    print()
    
    # 开始分析
    try:
        analyzer.analyze_all_papers()
        
        # 显示摘要
        summary = analyzer.get_analysis_summary()
        print("\n分析摘要:")
        print("=" * 30)
        for key, value in summary.items():
            print(f"{key}: {value}")
            
    except KeyboardInterrupt:
        print("\n用户中断分析")
        analyzer.save_results()
        analyzer.save_progress()
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        analyzer.save_results()
        analyzer.save_progress()

if __name__ == "__main__":
    main()
