# 论文研究假设分析器

这个工具可以自动分析PDF格式的学术论文，提取与研究假设和研究问题相关的所有信息。

## 功能特点

- 📄 **PDF文本提取**: 支持多种PDF文本提取方法
- 🤖 **AI智能分析**: 集成ChatGPT和DeepSeek API
- 💾 **增量分析**: 支持断点续传，避免重复分析
- 📊 **结构化输出**: 以JSON格式保存分析结果
- 🔄 **进度跟踪**: 记录分析进度，支持中断后继续

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置

1. 复制环境变量示例文件：
```bash
copy .env.example .env
```

2. 编辑 `.env` 文件，添加你的API密钥：
   - 如果使用OpenAI: 设置 `OPENAI_API_KEY`
   - 如果使用DeepSeek: 设置 `DEEPSEEK_API_KEY` (推荐，成本更低)

3. 在 `config.py` 中可以调整以下设置：
   - `DEFAULT_MODEL`: 选择使用的AI模型 ("openai" 或 "deepseek")
   - `CHUNK_SIZE`: PDF文本分块大小
   - `PDF_FOLDER`: PDF文件存放目录

## 使用方法

1. 将PDF文件放入 `doc_pdf` 文件夹
2. 运行分析器：
```bash
python paper_analyzer.py
```

## 输出格式

分析结果保存在 `research_analysis.json` 文件中，每个论文的分析结果包含：

```json
{
  "paper_name.pdf": {
    "research_hypotheses": [
      {
        "hypothesis": "具体的研究假设内容",
        "context": "假设提出的上下文",
        "page_reference": "页面或章节信息"
      }
    ],
    "research_questions": [
      {
        "question": "具体的研究问题",
        "context": "问题提出的背景",
        "page_reference": "页面或章节信息"
      }
    ],
    "research_background": {
      "problem_statement": "研究问题的陈述",
      "motivation": "研究动机",
      "gap_identified": "识别的研究空白",
      "theoretical_foundation": "理论基础"
    },
    "methodology_overview": {
      "approach": "研究方法概述",
      "design": "研究设计",
      "rationale": "方法选择的理由"
    },
    "key_contributions": ["主要贡献点"],
    "related_work_summary": "相关工作的简要总结",
    "limitations_mentioned": ["提到的局限性"],
    "file_path": "文件路径",
    "analysis_date": "分析日期",
    "text_length": "文本长度",
    "chunks_analyzed": "成功分析的文本块数量"
  }
}
```

## 文件说明

- `paper_analyzer.py`: 主分析脚本
- `pdf_extractor.py`: PDF文本提取模块
- `ai_analyzer.py`: AI分析模块
- `config.py`: 配置文件
- `research_analysis.json`: 分析结果输出文件
- `analysis_progress.json`: 分析进度记录文件

## 注意事项

1. **API成本**: 分析大量论文会产生API调用费用，建议先用少量文件测试
2. **文本质量**: PDF文本提取质量取决于PDF文件的质量
3. **分析时间**: 每个论文的分析时间取决于文本长度和API响应速度
4. **中断恢复**: 程序支持中断后继续分析，不会重复处理已完成的文件

## 故障排除

1. **PDF提取失败**: 尝试使用不同的PDF文件或检查文件是否损坏
2. **API调用失败**: 检查API密钥是否正确，网络连接是否正常
3. **内存不足**: 调整 `CHUNK_SIZE` 参数，减小文本块大小

## 扩展功能

可以通过修改 `ANALYSIS_PROMPT` 来调整分析的重点和输出格式，或者添加新的分析维度。
