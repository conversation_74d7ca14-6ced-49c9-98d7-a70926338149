import openai
import requests
import json
import time
from typing import Dict, List, Optional
import logging
from config import *

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AIAnalyzer:
    """AI分析器，支持OpenAI和DeepSeek API"""
    
    def __init__(self, model_type: str = DEFAULT_MODEL):
        self.model_type = model_type
        self.setup_client()
    
    def setup_client(self):
        """设置API客户端"""
        if self.model_type == "openai":
            if not OPENAI_API_KEY:
                raise ValueError("请设置OPENAI_API_KEY环境变量")
            openai.api_key = OPENAI_API_KEY
            self.client = openai.OpenAI(api_key=OPENAI_API_KEY)
            logger.info("使用OpenAI API")
        
        elif self.model_type == "deepseek":
            if not DEEPSEEK_API_KEY:
                raise ValueError("请设置DEEPSEEK_API_KEY环境变量")
            self.client = openai.OpenAI(
                api_key=DEEPSEEK_API_KEY,
                base_url=DEEPSEEK_BASE_URL
            )
            logger.info("使用DeepSeek API")
        
        else:
            raise ValueError(f"不支持的模型类型: {self.model_type}")
    
    def analyze_text_chunk(self, text_chunk: str, retry_count: int = 3) -> Optional[Dict]:
        """分析单个文本块"""
        prompt = ANALYSIS_PROMPT.format(text=text_chunk)
        
        for attempt in range(retry_count):
            try:
                print("@@@@@@@@@@@@")
                if self.model_type == "openai":
                    response = self.client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "你是一个专业的学术论文分析助手，擅长提取研究假设和相关信息。"},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.3,
                        max_tokens=2000
                    )
                    content = response.choices[0].message.content
                
                elif self.model_type == "deepseek":
                    response = self.client.chat.completions.create(
                        model="deepseek-chat",
                        messages=[
                            {"role": "system", "content": "你是一个专业的学术论文分析助手，擅长提取研究假设和相关信息。"},
                            {"role": "user", "content": prompt}
                        ],
                        temperature=0.3,
                        max_tokens=2000
                    )
                    content = response.choices[0].message.content
                
                # 尝试解析JSON
                try:
                    # 提取JSON部分（如果AI返回了额外的文本）
                    json_start = content.find('{')
                    json_end = content.rfind('}') + 1
                    if json_start != -1 and json_end != 0:
                        json_content = content[json_start:json_end]
                        result = json.loads(json_content)
                        return result
                    else:
                        logger.warning(f"无法找到JSON格式的响应")
                        return None
                
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败 (尝试 {attempt + 1}/{retry_count}): {e}")
                    if attempt == retry_count - 1:
                        # 最后一次尝试，返回原始文本
                        return {"raw_response": content, "parse_error": str(e)}
                    time.sleep(1)
                    continue
            
            except Exception as e:
                logger.error(f"API调用失败 (尝试 {attempt + 1}/{retry_count}): {e}")
                if attempt < retry_count - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    return None
        
        return None
    
    def merge_analysis_results(self, results: List[Dict]) -> Dict:
        """合并多个分析结果"""
        merged = {
            "research_hypotheses": [],
            "research_questions": [],
            "research_background": {},
            "methodology_overview": {},
            "key_contributions": [],
            "related_work_summary": "",
            "limitations_mentioned": []
        }
        
        for result in results:
            if not result or "parse_error" in result:
                continue
            
            # 合并研究假设
            if "research_hypotheses" in result and result["research_hypotheses"]:
                merged["research_hypotheses"].extend(result["research_hypotheses"])
            
            # 合并研究问题
            if "research_questions" in result and result["research_questions"]:
                merged["research_questions"].extend(result["research_questions"])
            
            # 合并研究背景（取最完整的）
            if "research_background" in result and result["research_background"]:
                for key, value in result["research_background"].items():
                    if value and (key not in merged["research_background"] or not merged["research_background"][key]):
                        merged["research_background"][key] = value
            
            # 合并方法论概述
            if "methodology_overview" in result and result["methodology_overview"]:
                for key, value in result["methodology_overview"].items():
                    if value and (key not in merged["methodology_overview"] or not merged["methodology_overview"][key]):
                        merged["methodology_overview"][key] = value
            
            # 合并关键贡献
            if "key_contributions" in result and result["key_contributions"]:
                merged["key_contributions"].extend(result["key_contributions"])
            
            # 合并相关工作总结
            if "related_work_summary" in result and result["related_work_summary"]:
                if merged["related_work_summary"]:
                    merged["related_work_summary"] += " " + result["related_work_summary"]
                else:
                    merged["related_work_summary"] = result["related_work_summary"]
            
            # 合并局限性
            if "limitations_mentioned" in result and result["limitations_mentioned"]:
                merged["limitations_mentioned"].extend(result["limitations_mentioned"])
        
        # 去重
        merged["research_hypotheses"] = self.remove_duplicates(merged["research_hypotheses"])
        merged["research_questions"] = self.remove_duplicates(merged["research_questions"])
        merged["key_contributions"] = list(set(merged["key_contributions"]))
        merged["limitations_mentioned"] = list(set(merged["limitations_mentioned"]))
        
        return merged
    
    def remove_duplicates(self, items: List[Dict]) -> List[Dict]:
        """移除重复项"""
        seen = set()
        unique_items = []
        
        for item in items:
            if isinstance(item, dict):
                # 使用主要内容作为去重标准
                key_content = item.get('hypothesis', '') + item.get('question', '')
                if key_content not in seen:
                    seen.add(key_content)
                    unique_items.append(item)
            else:
                if item not in seen:
                    seen.add(item)
                    unique_items.append(item)
        
        return unique_items
