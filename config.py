import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# API配置
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"

# 文件路径配置
PDF_FOLDER = "doc_pdf"
OUTPUT_JSON = "research_analysis.json"
PROGRESS_FILE = "analysis_progress.json"

# 分析配置
DEFAULT_MODEL = "openai"  # 可选: "openai" 或 "deepseek"
CHUNK_SIZE = 4000  # PDF文本分块大小
OVERLAP_SIZE = 200  # 分块重叠大小

# 提示词模板
ANALYSIS_PROMPT = """
请分析以下学术论文文本，提取与研究假设和研究问题相关的所有信息。请按以下结构返回JSON格式的分析结果：

{{
    "research_hypotheses": [
        {{
            "hypothesis": "具体的研究假设内容",
            "context": "假设提出的上下文",
            "page_reference": "如果能识别，提供页面或章节信息"
        }}
    ],
    "research_questions": [
        {{
            "question": "具体的研究问题",
            "context": "问题提出的背景",
            "page_reference": "如果能识别，提供页面或章节信息"
        }}
    ],
    "research_background": {{
        "problem_statement": "研究问题的陈述",
        "motivation": "研究动机",
        "gap_identified": "识别的研究空白",
        "theoretical_foundation": "理论基础"
    }},
    "methodology_overview": {{
        "approach": "研究方法概述",
        "design": "研究设计",
        "rationale": "方法选择的理由"
    }},
    "key_contributions": [
        "主要贡献点1",
        "主要贡献点2"
    ],
    "related_work_summary": "相关工作的简要总结",
    "limitations_mentioned": [
        "提到的局限性1",
        "提到的局限性2"
    ]
}}

论文文本：
{text}

请确保返回有效的JSON格式，如果某些信息在文本中没有找到，请用null或空数组表示。
"""
